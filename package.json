{"name": "web", "private": true, "type": "module", "scripts": {"dev": "react-router dev", "typecheck": "react-router typegen && tsc --noEmit"}, "dependencies": {"@auth/core": "^0.37.2", "@babel/generator": "^7.27.1", "@chakra-ui/react": "2.8.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@emotion/react": "11.11.4", "@emotion/styled": "11.11.0", "@hono/auth-js": "^1.0.15", "@lshay/ui": "^0.1.32", "@mastra/core": "^0.13.1", "@neondatabase/serverless": "^0.10.4", "@react-aria/button": "^3.13.1", "@react-router/fs-routes": "^7.6.0", "@react-router/node": "^7.6.0", "@react-router/serve": "^7.6.0", "@tanstack/react-query": "^5.72.2", "@tanstack/react-table": "^8.21.2", "@types/babel__core": "^7.20.5", "@vis.gl/react-google-maps": "^0.8.3", "argon2": "^0.43.0", "classnames": "^2.5.1", "clean-stack": "^5.2.0", "cmdk": "^1.1.1", "color2k": "^2.0.3", "date-fns": "^4.1.0", "downshift": "^9.0.9", "isbot": "^5.1.27", "lodash-es": "^4.17.21", "lucide-react": "0.358.0", "motion": "^12.7.4", "papaparse": "^5.5.2", "pdfjs-dist": "^4.9.124", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-idle-timer": "^5.7.2", "react-markdown": "^6.0.3", "react-resizable-panels": "^2.1.7", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "react-router-hono-server": "^2.13.0", "recharts": "^2.12.0", "remark-gfm": "4.0.0", "serialize-error": "^12.0.0", "sonner": "^2.0.3", "stripe": "^18.2.1", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.2.0", "three": "^0.175.0", "vaul": "^1.1.2", "ws": "^8.18.2", "yup": "^1.6.1", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1", "@react-router/dev": "^7.6.0", "@tailwindcss/vite": "^4.1.4", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/micromatch": "^4.0.9", "@types/node": "^20", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/ws": "^8.18.1", "autoprefixer": "^10.4.21", "babel-plugin-react-require": "^4.0.3", "fast-glob": "^3.3.3", "jsdom": "^26.1.0", "micromatch": "^4.0.8", "postcss": "^8.5.3", "tailwindcss": "3", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-plugin-babel": "^1.3.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "overrides": {"next-themes": "^0.3.0"}}