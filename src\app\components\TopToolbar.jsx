import React from "react";
import {
  FileText,
  Folder,
  Save,
  Download,
  RotateCcw,
  RotateCw,
  Grid3X3,
  ZoomOut,
  ZoomIn,
  <PERSON><PERSON>,
  <PERSON>u,
  Sidebar,
} from "lucide-react";

export default function TopToolbar({
  showLeftSidebar,
  toggleLeftSidebar,
  clearCanvas,
  undo,
  redo,
  canUndo,
  canRedo,
  exportAsText,
  copyToClipboard,
  selection,
  copySelection,
  clipboard,
  pasteSelectionCenter,
  showGrid,
  setShowGrid,
  zoom,
  setZoom,
  canvasScale,
  showAI,
  setShowAI,
  tools,
  selectedTool,
  setSelectedTool,
}) {
  return (
    <div className="border-b border-[#333333] bg-[#1a1a1a] px-2 sm:px-3 md:px-6 py-2 sm:py-3 flex-shrink-0 overflow-x-auto">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 md:space-x-4">
          <button
            onClick={toggleLeftSidebar}
            className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors lg:hidden"
            title="Toggle Sidebar (Ctrl+)"
          >
            <Menu size={16} />
          </button>

          <div className="flex items-center space-x-2">
            <div className="text-[#00ff00] font-bold text-lg">ASCII</div>
            <div className="text-[#666666] hidden sm:block">MAKER</div>
          </div>

          <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

          <div className="flex items-center space-x-1 overflow-x-auto scrollbar-hide">
            <button
              onClick={clearCanvas}
              className="px-2 md:px-3 py-1.5 md:py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors flex-shrink-0"
            >
              <FileText size={12} className="sm:hidden" />
              <FileText size={14} className="hidden sm:block" />
              <span className="hidden sm:inline">Clear</span>
            </button>

            <button className="px-2 md:px-3 py-1.5 md:py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors flex-shrink-0">
              <Folder size={12} className="sm:hidden" />
              <Folder size={14} className="hidden sm:block" />
              <span className="hidden sm:inline">Open</span>
            </button>

            <button className="px-2 md:px-3 py-1.5 md:py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors flex-shrink-0">
              <Save size={12} className="sm:hidden" />
              <Save size={14} className="hidden sm:block" />
              <span className="hidden sm:inline">Save</span>
            </button>

            <button
              onClick={exportAsText}
              className="px-2 md:px-3 py-1.5 md:py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors flex-shrink-0"
            >
              <Download size={12} className="sm:hidden" />
              <Download size={14} className="hidden sm:block" />
              <span className="hidden sm:inline">Export</span>
            </button>
          </div>

          <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

          <div className="flex items-center space-x-1 md:space-x-2">
            <button
              onClick={undo}
              disabled={canUndo === false}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] disabled:bg-[#1a1a1a] disabled:text-[#555555] border border-[#444444] rounded transition-colors"
              title="Undo (Ctrl+Z)"
            >
              <RotateCcw size={14} />
            </button>

            <button
              onClick={redo}
              disabled={canRedo === false}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] disabled:bg-[#1a1a1a] disabled:text-[#555555] border border-[#444444] rounded transition-colors"
              title="Redo (Ctrl+Shift+Z)"
            >
              <RotateCw size={14} />
            </button>
          </div>

          <div className="h-6 w-px bg-[#333333] hidden lg:block"></div>

          <div className="flex items-center space-x-1 md:space-x-2 overflow-hidden">
            <button
              onClick={copyToClipboard}
              className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
            >
              {/* Using plain text to keep deps local */}
              <span>⎘</span>
              <span className="hidden lg:inline">Copy All</span>
            </button>

            {selection && (
              <button
                onClick={copySelection}
                className="px-2 md:px-3 py-2 bg-[#00ff00] text-[#000000] hover:bg-[#00cc00] border border-[#00ff00] rounded text-xs flex items-center space-x-1 md:space-x-2 transition-colors"
              >
                <span>✂</span>
                <span className="hidden lg:inline">Copy Selection</span>
              </button>
            )}

            {clipboard && (
              <button
                onClick={pasteSelectionCenter}
                className="px-2 md:px-3 py-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-xs text-[#cccccc] flex items-center space-x-1 md:space-x-2 transition-colors"
              >
                <span>📋</span>
                <span className="hidden lg:inline">Paste</span>
              </button>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2 md:space-x-4">
          <button
            onClick={toggleLeftSidebar}
            className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors hidden lg:block"
            title="Toggle Sidebar (Ctrl+)"
          >
            <Sidebar size={16} />
          </button>

          <button
            onClick={() => setShowAI(!showAI)}
            className={`p-2 border border-[#444444] rounded transition-all relative ${
              showAI
                ? "bg-[#007acc] text-white border-[#007acc] shadow-md"
                : "bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] text-[#cccccc] hover:border-[#666666]"
            }`}
            title="AI Assistant"
          >
            <Bot size={16} />
            {showAI && (
              <div className="absolute -top-1 -right-1 w-2 h-2 bg-[#007acc] rounded-full ring-2 ring-[#1a1a1a]"></div>
            )}
          </button>

          <div className="h-6 w-px bg-[#333333] hidden md:block"></div>

          <div className="flex items-center space-x-1 md:space-x-2">
            <button
              onClick={() => setShowGrid(!showGrid)}
              className={`p-2 border border-[#444444] rounded transition-colors ${
                showGrid
                  ? "bg-[#00ff00] text-[#000000]"
                  : "bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] text-[#cccccc]"
              }`}
              title="Toggle Grid"
            >
              <Grid3X3 size={14} />
            </button>

            <button
              onClick={() => setZoom(Math.max(0.25, zoom - 0.25))}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors"
              title="Zoom Out"
            >
              <ZoomOut size={14} />
            </button>

            <span className="text-xs text-[#888888] min-w-[2rem] md:min-w-[3rem] text-center">
              {Math.round(zoom * canvasScale * 100)}%
            </span>

            <button
              onClick={() => setZoom(Math.min(2, zoom + 0.25))}
              className="p-2 bg-[#2a2a2a] hover:bg-[#3a3a3a] active:bg-[#4a4a4a] border border-[#444444] rounded text-[#cccccc] transition-colors"
              title="Zoom In"
            >
              <ZoomIn size={16} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}


