import React from 'react';
import { Github, Terminal } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0a0a0a] border-t border-[#333333] text-[#00ff00] font-mono">
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Terminal className="w-4 h-4 text-[#00ff00]" />
              <span className="text-[#00ff00] font-bold text-sm">ASCII MAKER</span>
            </div>
            <span className="text-[#666666] text-xs">© {currentYear}</span>
          </div>
          
          <div className="flex items-center space-x-4">
            <a 
              href="https://github.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-[#666666] hover:text-[#00ff00] transition-colors"
              title="GitHub"
            >
              <Github className="w-4 h-4" />
            </a>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-[#00ff00] rounded-full animate-pulse"></div>
              <span className="text-[#666666] text-xs">ONLINE</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}