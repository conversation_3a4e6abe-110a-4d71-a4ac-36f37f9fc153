export async function POST(request) {
  try {
    const { message, canvasSize, currentCanvas } = await request.json();

    if (!message) {
      return Response.json({ error: "Message is required" }, { status: 400 });
    }

    // Prepare context for AI
    const canvasText = currentCanvas
      ? currentCanvas.map((row) => row.join("")).join("\n")
      : "";

    const systemPrompt = `You are an expert ASCII art assistant. Generate only ASCII art unless the user asks for tips.

Canvas size: ${canvasSize.width}x${canvasSize.height} characters
Current canvas content:
${canvasText || "(empty canvas)"}

Rules for ASCII output:
- Fit within ${canvasSize.width} columns by ${canvasSize.height} rows
- Use simple ASCII characters (e.g., █▓▒░■□●○─│┌┐└┘╔╗╚╝║═╠╣╦╩╬┼★☆0-9A-Z)
- Keep designs simple and recognizable
- Do not include any explanations unless explicitly asked
`;

    // If Anthropic API key exists, call real AI; otherwise use examples below
    if (process.env.ANTHROPIC_API_KEY) {
      try {
        const anthropicBody = {
          model: 'claude-3-5-sonnet-20241022',
          max_tokens: 1024,
          messages: [
            { role: 'user', content: `${systemPrompt}\n\nUser request: ${message}` },
          ],
        };

        const resp = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'content-type': 'application/json',
            'x-api-key': process.env.ANTHROPIC_API_KEY,
            'anthropic-version': '2023-06-01',
          },
          body: JSON.stringify(anthropicBody),
        });

        if (!resp.ok) {
          const errText = await resp.text();
          console.error('Anthropic API error:', resp.status, errText);
          throw new Error('Anthropic request failed');
        }

        const json = await resp.json();
        // Extract first text block
        const text = Array.isArray(json.content)
          ? (json.content.find((p) => p.type === 'text')?.text || '')
          : '';

        // If user asked for ASCII, try to extract fenced block; otherwise use text
        const fenced = text.match(/```[\s\S]*?```/);
        const asciiArt = (fenced ? fenced[0].replace(/```/g, '') : text).trim();

        return Response.json({
          message: 'Here you go!',
          asciiArt: asciiArt || null,
        });
      } catch (err) {
        console.error('AI Integration error:', err);
        // Fall through to examples below
      }
    }

    // Basic pattern matching when no AI key is configured or API failed
    let response = {
      message:
        "I'd love to help you create ASCII art! However, to provide the best assistance, please select an AI integration first. In the meantime, here are some examples I can create:",
      asciiArt: null,
    };

    const lowerMessage = message.toLowerCase();

    // Enhanced ASCII art examples based on request
    if (lowerMessage.includes("cat")) {
      response = {
        message: "Here's a simple ASCII cat for you!",
        asciiArt: `    /\\_/\\  
   ( o.o ) 
    > ^ <  
  ~~~~~~~~~~`,
      };
    } else if (lowerMessage.includes("heart")) {
      response = {
        message: "Here's a heart in ASCII!",
        asciiArt: `  ♥♥   ♥♥  
 ♥♥♥♥ ♥♥♥♥ 
♥♥♥♥♥♥♥♥♥♥
 ♥♥♥♥♥♥♥♥♥ 
  ♥♥♥♥♥♥♥  
   ♥♥♥♥♥   
    ♥♥♥    
     ♥     `,
      };
    } else if (lowerMessage.includes("star")) {
      response = {
        message: "Here's a star pattern!",
        asciiArt: `    ★    
   ★★★   
  ★★★★★  
 ★★★★★★★ 
★★★★★★★★★
 ★★★★★★★ 
  ★★★★★  
   ★★★   
    ★    `,
      };
    } else if (
      lowerMessage.includes("house") ||
      lowerMessage.includes("home")
    ) {
      response = {
        message: "Here's a simple house!",
        asciiArt: `    /\\    
   /  \\   
  /____\\  
  |    |  
  |    |  
  | [] |  
  |____|  `,
      };
    } else if (lowerMessage.includes("tree")) {
      response = {
        message: "Here's a simple tree!",
        asciiArt: `    ★    
   ░░░   
  ░░░░░  
 ░░░░░░░ 
░░░░░░░░░
   |||   
   |||   `,
      };
    } else if (
      lowerMessage.includes("smiley") ||
      lowerMessage.includes("smile") ||
      lowerMessage.includes("face")
    ) {
      response = {
        message: "Here's a happy face!",
        asciiArt: `  ○○○○○  
 ○     ○ 
○ ●   ● ○
○       ○
○  \\_/  ○
 ○     ○ 
  ○○○○○  `,
      };
    } else if (lowerMessage.includes("flower")) {
      response = {
        message: "Here's a simple flower!",
        asciiArt: `   ●●●   
  ● ♥ ●  
   ●●●   
    |    
    |    
 ~~~~~~~~`,
      };
    } else if (lowerMessage.includes("diamond")) {
      response = {
        message: "Here's a diamond shape!",
        asciiArt: `    ♦    
   ♦♦♦   
  ♦♦♦♦♦  
 ♦♦♦♦♦♦♦ 
  ♦♦♦♦♦  
   ♦♦♦   
    ♦    `,
      };
    } else if (lowerMessage.includes("help") || lowerMessage.includes("tip")) {
      response = {
        message: `Here are some ASCII art tips:

• Use █ ▓ ▒ ░ for solid-to-transparent shading
• Try ● ○ for round shapes 
• Use ─ │ ┌ ┐ └ ┘ for box drawing
• ★ ☆ add sparkle effects
• Combine characters for texture
• Start simple, add details gradually
• Use symmetry for balanced designs

What would you like to create?`,
      };
    } else if (
      lowerMessage.includes("mountain") ||
      lowerMessage.includes("hills")
    ) {
      response = {
        message: "Here are some mountains!",
        asciiArt: `    /\\    
   /  \\   
  /    \\  
 /      \\ 
/        \\
~~~~~~~~~~`,
      };
    } else {
      // For other requests, provide helpful guidance
      response = {
        message: `I can help you create ASCII art! Try asking me to make:
• Animals (cat, dog, bird)
• Shapes (heart, star, diamond)
• Objects (house, tree, flower)
• Or ask for tips and techniques

What would you like me to create for you?`,
      };
    }

    return Response.json(response);
  } catch (error) {
    console.error("AI Assistant Error:", error);
    return Response.json(
      {
        error: "Failed to process AI request",
      },
      { status: 500 },
    );
  }
}
