import React from "react";
import { Bot, X, Send } from "lucide-react";

export default function AIPanel({
  isMobile,
  aiProvider,
  setAiProvider,
  aiMessages,
  aiLoading,
  aiInput,
  setAiInput,
  sendToAI,
  applyAIArt,
  close,
}) {
  return (
    <div className={`${isMobile ? 'fixed inset-0 z-40' : 'w-72 sm:w-80 lg:w-96 2xl:w-80'} border-l border-[#333333] bg-[#1a1a1a] flex flex-col flex-shrink-0 max-w-full`}>
      <div className="border-b border-[#333333] p-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-r from-[#00ff00] to-[#00cc00] rounded-full flex items-center justify-center">
            <Bot size={16} className="text-[#000000]" />
          </div>
          <div>
            <div className="text-[#00ff00] font-bold text-sm">ASCII Assistant</div>
            <div className="text-[#666666] text-xs">
              {aiProvider === 'anthropic' ? 'Anthropic Claude 3.5' : 'OpenAI GPT-4'} via Maestra AI
            </div>
          </div>
        </div>
        <button onClick={close} className="p-1 text-[#666666] hover:text-[#cccccc] transition-colors">
          <X size={20} />
        </button>
      </div>

      <div className="flex-1 overflow-y-auto p-2 sm:p-4 space-y-3 sm:space-y-4">
        {aiMessages.map((message, index) => (
          <div key={index} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[85%] sm:max-w-[80%] rounded-lg p-2 sm:p-3 ${message.role === 'user' ? 'bg-[#00ff00] text-[#000000]' : 'bg-[#2a2a2a] text-[#cccccc]'}`}>
              <div className="text-sm whitespace-pre-wrap">{message.content}</div>
              {message.asciiArt && (
                <div className="mt-2">
                  <div className="bg-[#0a0a0a] p-2 rounded text-[#00ff00] font-mono text-xs overflow-x-auto">
                    {message.asciiArt}
                  </div>
                  {message.action === 'apply' && (
                    <button
                      onClick={() => applyAIArt(message.asciiArt)}
                      className="mt-2 px-3 py-1 bg-[#00ff00] text-[#000000] rounded text-xs font-bold hover:bg-[#00cc00] transition-colors"
                    >
                      Apply to Canvas
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
        {aiLoading && (
          <div className="flex justify-start">
            <div className="bg-[#2a2a2a] text-[#cccccc] rounded-lg p-3">
              <div className="flex items-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-[#00ff00] border-t-transparent rounded-full"></div>
                <span className="text-sm">Generating...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="border-t border-[#333333] p-2 sm:p-4 flex-shrink-0">
        <div className="mb-3">
          <label className="text-xs text-[#888888] mb-1 block">AI Provider</label>
          <select
            value={aiProvider}
            onChange={(e) => setAiProvider(e.target.value)}
            className="w-full px-2 py-1 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-xs"
          >
            <option value="openai">OpenAI GPT-4</option>
            <option value="anthropic">Anthropic Claude 3.5 Sonnet</option>
          </select>
        </div>

        <div className="flex space-x-2">
          <input
            type="text"
            value={aiInput}
            onChange={(e) => setAiInput(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && sendToAI()}
            placeholder="Ask me to create ASCII art or help you..."
            className="flex-1 px-3 py-2 bg-[#2a2a2a] border border-[#444444] rounded text-[#cccccc] text-sm placeholder-[#666666] focus:border-[#00ff00] focus:outline-none"
          />
          <button
            onClick={sendToAI}
            disabled={!aiInput.trim() || aiLoading}
            className="px-4 py-2 bg-[#00ff00] text-[#000000] rounded font-bold disabled:bg-[#666666] disabled:text-[#333333] hover:bg-[#00cc00] transition-colors"
          >
            <Send size={16} />
          </button>
        </div>
        <div className="mt-2 text-xs text-[#666666]">
          Try: "Make a cat", "Create a heart", "Draw mountains", "Help me with shading"
          <div className="mt-1 text-[10px] text-[#555555]">
            Requires {aiProvider === 'anthropic' ? 'ANTHROPIC_API_KEY' : 'OPENAI_API_KEY'} env variable
          </div>
        </div>
      </div>
    </div>
  );
}


