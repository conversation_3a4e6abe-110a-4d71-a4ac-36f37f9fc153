import React, { forwardRef } from "react";

const CanvasGrid = forwardRef(function CanvasGrid(
  {
    canvas,
    canvasWidth,
    canvasHeight,
    cellSize,
    canvasScale,
    zoom,
    selection,
    cursorPos,
    showGrid,
    onCellMouseDown,
    onCellMouseEnter,
    onMouseUp,
    onMouseLeave,
  },
  ref
) {
  return (
    <div
      ref={ref}
      className="font-mono leading-none select-none bg-[#0a0a0a] border border-[#333333] shadow-2xl"
      style={{
        fontSize: `${Math.max(8, Math.min(14, cellSize.width * 1.2))}px`,
        lineHeight: `${cellSize.height}px`,
        letterSpacing: "0",
        width: `${canvasWidth * cellSize.width}px`,
        height: `${canvasHeight * cellSize.height}px`,
        transform: `scale(${canvasScale * zoom})`,
        transformOrigin: "center",
      }}
      onMouseUp={onMouseUp}
      onMouseLeave={onMouseLeave}
      onContextMenu={(e) => e.preventDefault()}
      onWheel={(e) => e.preventDefault()}
    >
      {canvas.map((row, rowIndex) => (
        <div key={rowIndex} className="flex">
          {row.map((cell, colIndex) => {
            const isInSelection =
              selection &&
              rowIndex >= Math.min(selection.startRow, selection.endRow) &&
              rowIndex <= Math.max(selection.startRow, selection.endRow) &&
              colIndex >= Math.min(selection.startCol, selection.endCol) &&
              colIndex <= Math.max(selection.startCol, selection.endCol);

            return (
              <div
                key={`${rowIndex}-${colIndex}`}
                className={`flex items-center justify-center cursor-crosshair transition-colors relative ${
                  showGrid ? "border border-[#333333]/30" : ""
                } ${
                  isInSelection
                    ? "bg-[#00ff00]/20 border-[#00ff00]/50"
                    : "hover:bg-[#333333]/50"
                } ${
                  cursorPos.x === colIndex && cursorPos.y === rowIndex
                    ? "bg-[#00ff00]/10"
                    : ""
                }`}
                style={{
                  width: `${cellSize.width}px`,
                  height: `${cellSize.height}px`,
                }}
                onMouseDown={(e) => onCellMouseDown(rowIndex, colIndex, e)}
                onMouseEnter={() => onCellMouseEnter(rowIndex, colIndex)}
              >
                <span className="text-[#00ff00] relative z-10">{cell}</span>
                {isInSelection && (
                  <div className="absolute inset-0 border border-[#00ff00] pointer-events-none" />
                )}
              </div>
            );
          })}
        </div>
      ))}
    </div>
  );
});

export default CanvasGrid;


